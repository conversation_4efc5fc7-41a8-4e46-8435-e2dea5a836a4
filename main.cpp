#include "login.h"
#include "mainwindow.h"
#include <QApplication>
#include <QLocale>
#include <QTranslator>
#include <QTcpSocket>
#include <QPointer>

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);
    QTranslator translator;
    const QStringList uiLanguages = QLocale::system().uiLanguages();
    for (const QString &locale : uiLanguages) {
        const QString baseName = "user_" + QLocale(locale).name();
        if (translator.load(":/i18n/" + baseName)) {
            a.installTranslator(&translator);
            break;
        }
    }

    // 创建第一个登录窗口
    Login *loginWindow1 = new Login(nullptr, 1);
    QPointer<MainWindow> mainWindow1 = nullptr;

    // 连接第一个登录窗口的登录成功信号
    QObject::connect(loginWindow1, &Login::loginSucceed, [&](const QString &account) {
        // 隐藏登录窗口
        loginWindow1->hide();

        // 创建主窗口
        if (!mainWindow1) {
            mainWindow1 = new MainWindow(account, nullptr, 1);

            // 连接返回登录界面的信号
            QObject::connect(mainWindow1, &MainWindow::returnToLogin, [&]() {
                // 重新显示登录窗口
                loginWindow1->show();
                // 清空输入框
                loginWindow1->findChild<QLineEdit*>("line_qqnum")->clear();
                loginWindow1->findChild<QLineEdit*>("line_password")->clear();
                // 重置登录窗口状态
                loginWindow1->findChild<QLineEdit*>("line_qqnum")->setText("请输入你的账号");
                loginWindow1->findChild<QLineEdit*>("line_password")->setText("请输入你的密码");
            });

            mainWindow1->show();
        }
    });

    // 创建第二个登录窗口
    Login *loginWindow2 = new Login(nullptr, 2);
    QPointer<MainWindow> mainWindow2 = nullptr;

    // 连接第二个登录窗口的登录成功信号
    QObject::connect(loginWindow2, &Login::loginSucceed, [&](const QString &account) {
        // 隐藏登录窗口
        loginWindow2->hide();

        // 创建主窗口
        if (!mainWindow2) {
            mainWindow2 = new MainWindow(account, nullptr, 2);

            // 连接返回登录界面的信号
            QObject::connect(mainWindow2, &MainWindow::returnToLogin, [&]() {
                // 重新显示登录窗口
                loginWindow2->show();
                // 清空输入框
                loginWindow2->findChild<QLineEdit*>("line_qqnum")->clear();
                loginWindow2->findChild<QLineEdit*>("line_password")->clear();
                // 重置登录窗口状态
                loginWindow2->findChild<QLineEdit*>("line_qqnum")->setText("请输入你的账号");
                loginWindow2->findChild<QLineEdit*>("line_password")->setText("请输入你的密码");
            });

            mainWindow2->show();
        }
    });

    loginWindow1->show();
    loginWindow2->show();
    return a.exec();
}
